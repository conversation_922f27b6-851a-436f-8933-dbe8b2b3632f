const REACT_APP_ZENDESK_CHAT_KEY_CODES = {
  Et: '99d59513-684a-4c02-b180-b56c5647ec45',
  Lv: '45f260ca-75ad-43a4-8cf2-fd37dc5ca5a6',
  Lt: '7eb5dc2a-382d-4ffb-a0af-df421d0e3ee0',
};

const REACT_APP_RUDDERSTACK_PLANE_URL =
  'https://estomikkxgm.dataplane.rudderstack.com';

const REACT_APP_RUDDERSTACK_API_KEYS = {
  Dekker: '2MFhxYRz2Z3vRnYw1ADQ5zbFuXt',
  Et: '2PCDVVz3bZCoyDRpTIjJll1dygV',
  Lv: '2PCDYKbCuwW7BaFy8yj1YUJQ2vj',
  Lt: '2PCDX8QT3Rkl4vhL6UDylQUU74t',
};

const REACT_APP_REGIONS = {
  Et: 'EE',
  Lv: 'LV',
  Lt: 'LT',
};

const REACT_APP_REGION_LANGUAGES = {
  Et: 'et',
  Lv: 'lv',
  Lt: 'lt',
};

const REACT_APP_PHONE_PREFIXES = {
  Et: '+372',
  Lv: '+371',
  Lt: '+370',
};

const SMALL_LOAN_INTEREST_RATES = {
  Et: 0.2,
  Lv: 0.24,
  Lt: 0.24,
};
const FAST_LOAN_INTEREST_RATES = {
  Et: 0.24,
  Lv: 0.24,
  Lt: 0.24,
};
const RENOVATION_LOAN_INTEREST_RATES = {
  Et: 0.2,
  Lv: 0.2,
  Lt: 0.2,
};
const VEHICLE_LOAN_INTEREST_RATES = {
  Et: 0.22,
  Lv: 0.22,
  Lt: 0.22,
};
const TRAVEL_LOAN_INTEREST_RATES = {
  Et: 0.2,
  Lv: 0.24,
  Lt: 0.24,
};
const HEALTH_LOAN_INTEREST_RATES = {
  Et: 0.2,
  Lv: 0.24,
  Lt: 0.24,
};
const BEAUTY_LOAN_INTEREST_RATES = {
  Et: 0.2,
  Lv: 0.24,
  Lt: 0.24,
};

const REACT_APP_ID_CARD_LOGIN_ENDPOINTS = {
  Et: 'https://id.esto.ee',
};

const DEKKER_CONFIG = {
  REACT_APP_CP_CA_ONBOARDING: 1,
  REACT_APP_REGION: REACT_APP_REGIONS.Et,
  REACT_APP_REGION_LANGUAGE: REACT_APP_REGION_LANGUAGES.Lv,
  REACT_APP_CORE_API_URL: 'https://api.dekker-dev1.ee',
  REACT_APP_CORE_API_ENDPOINT: 'https://api.dekker-dev1.ee/graphql',
  REACT_APP_PURCHASE_FLOW_API_ENDPOINT: 'https://pf-api.dekker-dev1.ee/graphql',
  REACT_APP_SENTRY_DSN:
    'https://<EMAIL>/6434227',
  REACT_APP_LANGUAGES: ['et', 'en', 'lt', 'lv', 'ru'],
  REACT_APP_LOCIZE_PROJECT_ID: '2f5ba6fb-f7a2-450b-972d-54c592d0e07d',
  REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.dekker-dev1.ee',
  REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.dekker-dev1.ee',
  REACT_APP_CONTACT_EMAIL: '<EMAIL>',
  REACT_APP_CONTACT_NAME: 'ESTO AS',
  REACT_APP_CONTACT_PHONE: '(+372) 622 52 52',
  REACT_APP_CONTACT_ADDRESS: 'Harju maakond, Tallinn, Tornimäe tn 2, 15010',
  REACT_APP_WEB_URL: 'https://user.v2.dekker-dev1.ee',
  REACT_APP_ZENDESK_CHAT_KEY_CODE: REACT_APP_ZENDESK_CHAT_KEY_CODES.Et,
  REACT_APP_SIFT_BEACON_KEY: '632061f0910c1f0c9fa6d675',
  REACT_APP_SMALL_LOAN_INTEREST_RATE: SMALL_LOAN_INTEREST_RATES.Lv,
  REACT_APP_FAST_LOAN_INTEREST_RATE: FAST_LOAN_INTEREST_RATES.Lv,
  REACT_APP_RENOVATION_LOAN_INTEREST_RATE: RENOVATION_LOAN_INTEREST_RATES.Lv,
  REACT_APP_VEHICLE_LOAN_INTEREST_RATE: VEHICLE_LOAN_INTEREST_RATES.Lv,
  REACT_APP_TRAVEL_LOAN_INTEREST_RATE: TRAVEL_LOAN_INTEREST_RATES.Lv,
  REACT_APP_HEALTH_LOAN_INTEREST_RATE: HEALTH_LOAN_INTEREST_RATES.Lv,
  REACT_APP_BEAUTY_LOAN_INTEREST_RATE: BEAUTY_LOAN_INTEREST_RATES.Lv,
  REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Et,
  REACT_APP_FAQ_URL_EN: 'https://estoee.zendesk.com/hc/et',
  REACT_APP_FAQ_URL_ET: 'https://estoee.zendesk.com/hc/et',
  REACT_APP_FAQ_URL_RU: 'https://estoee.zendesk.com/hc/et',
  REACT_APP_CL_FAQ_URL_EN:
    'https://estoee.zendesk.com/hc/et/categories/5515691021213',
  REACT_APP_CL_FAQ_URL_ET:
    'https://estoee.zendesk.com/hc/et/categories/5515691021213',
  REACT_APP_CL_FAQ_URL_RU:
    'https://estoee.zendesk.com/hc/et/categories/5515691021213',
  REACT_APP_HOMEPAGE_CREDIT_LINE_URL_EN:
    'https://esto.eu/ee/kliendile/krediidiliin?lang=en',
  REACT_APP_HOMEPAGE_CREDIT_LINE_URL_ET:
    'https://esto.eu/ee/kliendile/krediidiliin?lang=et',
  REACT_APP_HOMEPAGE_CREDIT_LINE_URL_RU:
    'https://esto.eu/ee/kliendile/krediidiliin?lang=ru',
  REACT_APP_TERMS_URL_EN:
    'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
  REACT_APP_TERMS_URL_ET:
    'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=et',
  REACT_APP_TERMS_URL_RU:
    'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=ru',
  REACT_APP_TERMS_URL_LV: 'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=lv',
  REACT_APP_TERMS_URL_LT: 'https://esto.eu/lt/taisykles-ir-salygos?lang=lt',
  REACT_APP_PRIVACY_POLICY_URL:
    'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/Rules%20of%20processing%20personal%20data.pdf',
  REACT_APP_RUDDERSTACK_PLANE_URL,
  REACT_APP_CREDY_TERMS_URL_LT: 'https://credy24.lt/terms',
  REACT_APP_RUDDERSTACK_API_KEY: REACT_APP_RUDDERSTACK_API_KEYS.Dekker,
  REACT_APP_ID_CARD_LOGIN_ENDPOINT: REACT_APP_ID_CARD_LOGIN_ENDPOINTS.Et,
  REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
  REACT_APP_SENTRY_AUTH_TOKEN:
    '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',
};

// If this sort of error below appears - then check is everything right in export
// Error: Failed to find .rc file at default paths: [./.env-cmdrc,./.env-cmdrc.js,./.env-cmdrc.json]

// eslint-disable-next-line no-undef
module.exports = {
  local: {
    ...DEKKER_CONFIG,
    REACT_APP_ENVIRONMENT: 'local',
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.lt',
  },
  'dekker-dev1': {
    ...DEKKER_CONFIG,
    REACT_APP_ENVIRONMENT: 'dekker-dev1',
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.dekker-dev1.ee',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.dekker-dev1.ee',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO LV AS',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Et}) 66 222 250`,
    REACT_APP_CONTACT_ADDRESS: 'Gustava Zemgala gatve 74, Rīga, LV-1039',
    REACT_APP_WEB_URL: 'https://user.v2.dekker-dev1.ee',
    REACT_APP_CORE_API_URL: 'https://api.dekker-dev1.ee',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.dekker-dev1.ee/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT:
      'https://pf-api.dekker-dev1.ee/graphql',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Et,
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.lt',
  },
  'dekker-dev3': {
    ...DEKKER_CONFIG,
    REACT_APP_ENVIRONMENT: 'dekker-dev3',
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.dekker-dev3.ee',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.dekker-dev3.ee',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO LV AS',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Et}) 66 222 250`,
    REACT_APP_CONTACT_ADDRESS: 'Gustava Zemgala gatve 74, Rīga, LV-1039',
    REACT_APP_WEB_URL: 'https://user.v2.dekker-dev3.ee',
    REACT_APP_CORE_API_URL: 'https://api.dekker-dev3.ee',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.dekker-dev3.ee/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT:
      'https://pf-api.dekker-dev3.ee/graphql',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Et,
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.ee',
  },
  'production-et': {
    ...DEKKER_CONFIG,
    REACT_APP_CP_CA_ONBOARDING: 0,
    REACT_APP_ENVIRONMENT: 'production-et',
    REACT_APP_REGION: REACT_APP_REGIONS.Et,
    REACT_APP_REGION_LANGUAGE: REACT_APP_REGION_LANGUAGES.Et,
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.esto.ee',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.esto.ee',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO AS',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Et}) 622 52 52`,
    REACT_APP_CONTACT_ADDRESS: 'Harju maakond, Tallinn, Laeva tn 2, 10111',
    REACT_APP_WEB_URL: 'https://user.v2.esto.ee',
    REACT_APP_CORE_API_URL: 'https://api.esto.ee',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.esto.ee/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT: 'https://pf-api.esto.ee/graphql',
    REACT_APP_ZENDESK_CHAT_KEY_CODE: REACT_APP_ZENDESK_CHAT_KEY_CODES.Et,
    REACT_APP_GOOGLE_ANALYTICS_ID: 'G-HMRMP25XHT',
    REACT_APP_GOOGLE_TAG_ID: 'GTM-NSTB5ZL',
    REACT_APP_SMALL_LOAN_INTEREST_RATE: SMALL_LOAN_INTEREST_RATES.Et,
    REACT_APP_FAST_LOAN_INTEREST_RATE: FAST_LOAN_INTEREST_RATES.Et,
    REACT_APP_RENOVATION_LOAN_INTEREST_RATE: RENOVATION_LOAN_INTEREST_RATES.Et,
    REACT_APP_VEHICLE_LOAN_INTEREST_RATE: VEHICLE_LOAN_INTEREST_RATES.Et,
    REACT_APP_TRAVEL_LOAN_INTEREST_RATE: TRAVEL_LOAN_INTEREST_RATES.Et,
    REACT_APP_HEALTH_LOAN_INTEREST_RATE: HEALTH_LOAN_INTEREST_RATES.Et,
    REACT_APP_BEAUTY_LOAN_INTEREST_RATE: BEAUTY_LOAN_INTEREST_RATES.Et,
    REACT_APP_SIFT_BEACON_KEY: '6cc9ff26cf',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Et,
    REACT_APP_FAQ_URL_EN: 'https://estoee.zendesk.com/hc/et',
    REACT_APP_FAQ_URL_ET: 'https://estoee.zendesk.com/hc/et',
    REACT_APP_FAQ_URL_RU: 'https://estoee.zendesk.com/hc/et',
    REACT_APP_CL_FAQ_URL_EN:
      'https://estoee.zendesk.com/hc/et/categories/5515691021213',
    REACT_APP_CL_FAQ_URL_ET:
      'https://estoee.zendesk.com/hc/et/categories/5515691021213',
    REACT_APP_CL_FAQ_URL_RU:
      'https://estoee.zendesk.com/hc/et/categories/5515691021213',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_EN:
      'https://esto.eu/ee/kliendile/krediidiliin?lang=en',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_ET:
      'https://esto.eu/ee/kliendile/krediidiliin?lang=et',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_RU:
      'https://esto.eu/ee/kliendile/krediidiliin?lang=ru',
    REACT_APP_TERMS_URL_EN:
      'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=en',
    REACT_APP_TERMS_URL_ET:
      'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=et',
    REACT_APP_TERMS_URL_RU:
      'https://esto.eu/ee/teabe-kasutamise-tingimused?lang=ru',
    REACT_APP_PRIVACY_POLICY_URL:
      'https://esto-public.s3.eu-west-2.amazonaws.com/docs/en-us/Rules%20of%20processing%20personal%20data.pdf',
    REACT_APP_RUDDERSTACK_API_KEY: REACT_APP_RUDDERSTACK_API_KEYS.Et,
    REACT_APP_ID_CARD_LOGIN_ENDPOINT: REACT_APP_ID_CARD_LOGIN_ENDPOINTS.Et,
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.ee',
    REACT_APP_POSTHOG_KEY: 'phc_5IMpqQRAqWDOIJwS60oJXQXB115C4iBRPZBPlmyfJM0',
    REACT_APP_POSTHOG_HOST: 'https://api-ph.esto.ee',
  },
  'production-lv': {
    ...DEKKER_CONFIG,
    REACT_APP_CP_CA_ONBOARDING: 1,
    REACT_APP_ENVIRONMENT: 'production-lv',
    REACT_APP_REGION: REACT_APP_REGIONS.Lv,
    REACT_APP_REGION_LANGUAGE: REACT_APP_REGION_LANGUAGES.Lv,
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.esto.lv',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.esto.lv',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO LV AS',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Lv}) 66 222 250`,
    REACT_APP_CONTACT_ADDRESS: 'Gustava Zemgala gatve 74, Rīga, LV-1039',
    REACT_APP_WEB_URL: 'https://user.v2.esto.lv',
    REACT_APP_CORE_API_URL: 'https://api.esto.lv',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.esto.lv/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT: 'https://pf-api.esto.lv/graphql',
    REACT_APP_ZENDESK_CHAT_KEY_CODE: REACT_APP_ZENDESK_CHAT_KEY_CODES.Lv,
    REACT_APP_GOOGLE_ANALYTICS_ID: 'G-3J2FXKL225',
    REACT_APP_GOOGLE_TAG_ID: 'GTM-NSTB5ZL',
    REACT_APP_SMALL_LOAN_INTEREST_RATE: SMALL_LOAN_INTEREST_RATES.Lv,
    REACT_APP_FAST_LOAN_INTEREST_RATE: FAST_LOAN_INTEREST_RATES.Lv,
    REACT_APP_RENOVATION_LOAN_INTEREST_RATE: RENOVATION_LOAN_INTEREST_RATES.Lv,
    REACT_APP_VEHICLE_LOAN_INTEREST_RATE: VEHICLE_LOAN_INTEREST_RATES.Lv,
    REACT_APP_TRAVEL_LOAN_INTEREST_RATE: TRAVEL_LOAN_INTEREST_RATES.Lv,
    REACT_APP_HEALTH_LOAN_INTEREST_RATE: HEALTH_LOAN_INTEREST_RATES.Lv,
    REACT_APP_BEAUTY_LOAN_INTEREST_RATE: BEAUTY_LOAN_INTEREST_RATES.Lv,
    REACT_APP_SIFT_BEACON_KEY: '6cc9ff26cf',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Lv,
    REACT_APP_FAQ_URL_EN: 'https://estolv.zendesk.com/hc/lv',
    REACT_APP_FAQ_URL_LV: 'https://estolv.zendesk.com/hc/lv',
    REACT_APP_FAQ_URL_RU: 'https://estolv.zendesk.com/hc/lv',
    REACT_APP_CL_FAQ_URL_EN:
      'https://estolv.zendesk.com/hc/lv/categories/17501723714717',
    REACT_APP_CL_FAQ_URL_LV:
      'https://estolv.zendesk.com/hc/lv/categories/17501723714717',
    REACT_APP_CL_FAQ_URL_RU:
      'https://estolv.zendesk.com/hc/lv/categories/17501723714717',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_EN:
      'https://esto.eu/lv/klientiem/kreditlinija?lang=en',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_ET:
      'https://esto.eu/lv/klientiem/kreditlinija?lang=et',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_RU:
      'https://esto.eu/lv/klientiem/kreditlinija?lang=ru',
    REACT_APP_TERMS_URL_EN:
      'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=en',
    REACT_APP_TERMS_URL_LV:
      'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=lv',
    REACT_APP_TERMS_URL_RU:
      'https://esto.eu/lv/noteikumi-un-nosacijumi?lang=ru',
    REACT_APP_PRIVACY_POLICY_URL:
      'https://esto-lv-public.s3.eu-central-1.amazonaws.com/docs/en-us/Privacy%20Policy.pdf',
    REACT_APP_RUDDERSTACK_API_KEY: REACT_APP_RUDDERSTACK_API_KEYS.Lv,
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 1,
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.lv',
    REACT_APP_POSTHOG_KEY: 'phc_Aehj7YBFzJ11zzW0WGa9pjB4EDxyIo9JaQPTcTjszAK',
    REACT_APP_POSTHOG_HOST: 'https://api-ph.esto.lv',
  },
  'production-lt-new': {
    ...DEKKER_CONFIG,
    REACT_APP_CP_CA_ONBOARDING: 1,
    REACT_APP_ENVIRONMENT: 'production-lt-new',
    REACT_APP_REGION: REACT_APP_REGIONS.Lt,
    REACT_APP_REGION_LANGUAGE: REACT_APP_REGION_LANGUAGES.Lt,
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.esto.lt',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.esto.lt',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO UAB',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Lt}) 524 09 988`,
    REACT_APP_CONTACT_ADDRESS: 'Lvivo g. 25, LT-09320 Vilnius, Lietuva',
    REACT_APP_WEB_URL: 'https://user.v2.esto.lt',
    REACT_APP_CORE_API_URL: 'https://api.esto.lt',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.esto.lt/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT: 'https://pf-api.esto.lt/graphql',
    REACT_APP_ZENDESK_CHAT_KEY_CODE: REACT_APP_ZENDESK_CHAT_KEY_CODES.Lt,
    REACT_APP_GOOGLE_ANALYTICS_ID: 'G-EDXPHSVQ7J',
    REACT_APP_GOOGLE_TAG_ID: 'GTM-NSTB5ZL',
    REACT_APP_SMALL_LOAN_INTEREST_RATE: SMALL_LOAN_INTEREST_RATES.Lt,
    REACT_APP_FAST_LOAN_INTEREST_RATE: FAST_LOAN_INTEREST_RATES.Lt,
    REACT_APP_RENOVATION_LOAN_INTEREST_RATE: RENOVATION_LOAN_INTEREST_RATES.Lt,
    REACT_APP_VEHICLE_LOAN_INTEREST_RATE: VEHICLE_LOAN_INTEREST_RATES.Lt,
    REACT_APP_TRAVEL_LOAN_INTEREST_RATE: TRAVEL_LOAN_INTEREST_RATES.Lt,
    REACT_APP_HEALTH_LOAN_INTEREST_RATE: HEALTH_LOAN_INTEREST_RATES.Lt,
    REACT_APP_BEAUTY_LOAN_INTEREST_RATE: BEAUTY_LOAN_INTEREST_RATES.Lt,
    REACT_APP_SIFT_BEACON_KEY: '6cc9ff26cf',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Lt,
    REACT_APP_FAQ_URL_EN: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_FAQ_URL_LT: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_FAQ_URL_RU: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_CL_FAQ_URL_EN:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_CL_FAQ_URL_LT:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_CL_FAQ_URL_RU:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_EN:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=en',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_ET:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=et',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_RU:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=ru',
    REACT_APP_TERMS_URL_EN: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
    REACT_APP_TERMS_URL_LT: 'https://esto.eu/lt/taisykles-ir-salygos?lang=lt',
    REACT_APP_TERMS_URL_RU: 'https://esto.eu/lt/taisykles-ir-salygos?lang=ru',
    REACT_APP_CREDY_TERMS_URL_LT: 'https://credy24.lt/terms',
    REACT_APP_PRIVACY_POLICY_URL:
      'https://esto-lt-public.s3.eu-central-1.amazonaws.com/docs/lt-lt/ESTO%20Privatumo%20politika.pdf',
    REACT_APP_RUDDERSTACK_API_KEY: REACT_APP_RUDDERSTACK_API_KEYS.Lt,
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
    REACT_APP_HOMEPAGE_REGION_URL: 'https://esto.lt',
    REACT_APP_POSTHOG_KEY: 'phc_vU210kf8c7re58ovGnGY39FoQ7Y94FMhoiJ204NexdJ',
    REACT_APP_POSTHOG_HOST: 'https://api-ph.esto.lt',
  },
  'staging-lt': {
    ...DEKKER_CONFIG,
    REACT_APP_CP_CA_ONBOARDING: 1,
    REACT_APP_ENVIRONMENT: 'staging-lt',
    REACT_APP_REGION: REACT_APP_REGIONS.Lt,
    REACT_APP_REGION_LANGUAGE: REACT_APP_REGION_LANGUAGES.Lt,
    REACT_APP_CUSTOMER_PROFILE_URL: 'https://profile.dekker.lt',
    REACT_APP_NEW_CUSTOMER_PROFILE_URL: 'https://app.dekker.lt',
    REACT_APP_CONTACT_EMAIL: '<EMAIL>',
    REACT_APP_CONTACT_NAME: 'ESTO UAB',
    REACT_APP_CONTACT_PHONE: `(${REACT_APP_PHONE_PREFIXES.Lt}) 524 09 988`,
    REACT_APP_CONTACT_ADDRESS: 'Lvivo g. 25, LT-09320 Vilnius, Lietuva',
    REACT_APP_WEB_URL: 'https://user.v2.dekker.lt',
    REACT_APP_CORE_API_URL: 'https://api.dekker.lt',
    REACT_APP_CORE_API_ENDPOINT: 'https://api.dekker.lt/graphql',
    REACT_APP_PURCHASE_FLOW_API_ENDPOINT: 'https://pf-api.dekker.lt/graphql',
    REACT_APP_ZENDESK_CHAT_KEY_CODE: REACT_APP_ZENDESK_CHAT_KEY_CODES.Lt,
    REACT_APP_SMALL_LOAN_INTEREST_RATE: SMALL_LOAN_INTEREST_RATES.Lt,
    REACT_APP_FAST_LOAN_INTEREST_RATE: FAST_LOAN_INTEREST_RATES.Lt,
    REACT_APP_RENOVATION_LOAN_INTEREST_RATE: RENOVATION_LOAN_INTEREST_RATES.Lt,
    REACT_APP_VEHICLE_LOAN_INTEREST_RATE: VEHICLE_LOAN_INTEREST_RATES.Lt,
    REACT_APP_TRAVEL_LOAN_INTEREST_RATE: TRAVEL_LOAN_INTEREST_RATES.Lt,
    REACT_APP_HEALTH_LOAN_INTEREST_RATE: HEALTH_LOAN_INTEREST_RATES.Lt,
    REACT_APP_BEAUTY_LOAN_INTEREST_RATE: BEAUTY_LOAN_INTEREST_RATES.Lt,
    REACT_APP_SIFT_BEACON_KEY: '',
    REACT_APP_PHONE_PREFIX: REACT_APP_PHONE_PREFIXES.Lt,
    REACT_APP_FAQ_URL_EN: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_FAQ_URL_LT: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_FAQ_URL_RU: 'https://estolt.zendesk.com/hc/lt',
    REACT_APP_CL_FAQ_URL_EN:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_CL_FAQ_URL_LT:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_CL_FAQ_URL_RU:
      'https://estolt.zendesk.com/hc/lt/categories/6075973726621',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_EN:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=en',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_ET:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=et',
    REACT_APP_HOMEPAGE_CREDIT_LINE_URL_RU:
      'https://esto.eu/lt/asmeninis/kredito-linija?lang=ru',
    REACT_APP_TERMS_URL_EN: 'https://esto.eu/lt/taisykles-ir-salygos?lang=en',
    REACT_APP_TERMS_URL_LT: 'https://esto.eu/lt/taisykles-ir-salygos?lang=lt',
    REACT_APP_TERMS_URL_RU: 'https://esto.eu/lt/taisykles-ir-salygos?lang=ru',
    REACT_APP_ONLY_PASSWORD_SIGNING_ENABLED: 0,
  },
};
