import { AppSearchParams } from 'app-constants';
import { AppLoader } from 'components';
import { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  getAndRemoveEParakstsOriginalUri,
  replaceOrAddQueryParamsInUrl,
} from 'services';

export const EParakstsLoginPage = () => {
  const navigate = useNavigate();
  const originalUri = getAndRemoveEParakstsOriginalUri();
  const [searchParams] = useSearchParams();
  const code = searchParams.get(AppSearchParams.eParakstsCode) ?? '';

  useEffect(() => {
    if (originalUri) {
      const finalUri = code
        ? replaceOrAddQueryParamsInUrl({
            url: originalUri,
            params: {
              [AppSearchParams.eParakstsCode]: code,
            },
            returnWithBase: true,
          })
        : originalUri;

      window.location.href = finalUri;
    } else {
      navigate('/');
    }
  }, [navigate, code]);

  return <AppLoader />;
};

export default EParakstsLoginPage;
