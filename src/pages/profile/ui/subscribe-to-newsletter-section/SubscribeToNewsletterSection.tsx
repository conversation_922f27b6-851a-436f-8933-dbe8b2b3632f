import { Typography } from '@components/typography';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { LOCIZE_PROFILE_PAGE_KEYS } from '@config/locize/profile-page';
import { NewsletterSubscriptionSwitch } from '@features/newsletter';
import { getRouteApi } from '@tanstack/react-router';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then(
    ({ SubscribeNewsletterPopup }) => ({
      default: SubscribeNewsletterPopup,
    }),
  ),
);

const routeApi = getRouteApi('/_protected/_main/profile');

export const SubscribeToNewsletterSection = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.profilePage);
  const navigate = routeApi.useNavigate();

  return (
    <>
      <div className="p-6 md:p-0 md:mt-6">
        <div className="p-6 border-neutral-200 border rounded-3xl">
          <p className="flex items-center justify-between gap-2">
            <Typography className="flex-1" affects="semibold" tag="span">
              {t(LOCIZE_PROFILE_PAGE_KEYS.subscribeToNewsletterSection.title)}
            </Typography>
            <NewsletterSubscriptionSwitch
              onNotEnoughDataError={() =>
                navigate({
                  search: {
                    showNewsletterPopup: true,
                  },
                })
              }
            />
          </p>
          <Typography className="mt-4" variant="text-s">
            {t(
              LOCIZE_PROFILE_PAGE_KEYS.subscribeToNewsletterSection.description,
            )}
          </Typography>
        </div>
      </div>
      <Suspense>
        <SubscribeNewsletterPopup />
      </Suspense>
    </>
  );
};
