import { Helmet } from '@components/Helmet';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { CreditLineCardSkeleton } from '@entities/credit-line';
import { useFeatureToggles } from '@hooks/system';
import {
  ActiveAgreementsList,
  ActiveAgreementsSkeleton,
} from '@widgets/active-agreements';
import { LoanOffers, LoanOffersSkeleton } from '@widgets/loan-offers';
import { lazy, Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { DashboardCreditLineBalance } from './dashboard-credit-line-balance';
import {
  DashboardSubscriptionsCarousel,
  DashboardSubscriptionsCarouselSkeleton,
} from './dashboard-subscriptions-carousel';
const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then(
    ({ SubscribeNewsletterPopup }) => ({
      default: SubscribeNewsletterPopup,
    }),
  ),
);

export const DashboardPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);
  const { agreementsFeature } = useFeatureToggles();

  return (
    <>
      <Helmet title={t(LOCIZE_DASHBOARD_KEYS.pageTitle)} />
      <div className="flex flex-col md:gap-20 md:p-12 md:pb-20">
        <Suspense fallback={<CreditLineCardSkeleton />}>
          <DashboardCreditLineBalance />
        </Suspense>

        <div className="z-[1] pb-10 grid pt-6 bg-primary-white rounded-t-3xl shadow-[0px_-10px_20px_0px_rgba(42,_40,_135,_0.10)] md:pb-0 md:pt-0 md:shadow-none md:gap-20">
          <Suspense fallback={<DashboardSubscriptionsCarouselSkeleton />}>
            <DashboardSubscriptionsCarousel />
          </Suspense>

          <Suspense fallback={<LoanOffersSkeleton />}>
            <LoanOffers />
          </Suspense>

          {agreementsFeature ? (
            <Suspense fallback={<ActiveAgreementsSkeleton />}>
              <ActiveAgreementsList />
            </Suspense>
          ) : null}

          <Suspense>
            <SubscribeNewsletterPopup />
          </Suspense>
        </div>
      </div>
    </>
  );
};
