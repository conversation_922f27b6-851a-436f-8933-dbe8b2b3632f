import { useIsUserSubscribedToNewsletter } from '@entities/user';
import { useNavigate, useRouterState } from '@tanstack/react-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useLocalStorage } from 'react-use';

const INITIAL_DELAY = 5000;

export const useIsSubscribeNewslettersPopupShown = () => {
  const navigate = useNavigate();
  const routerState = useRouterState();
  const showNewsletterPopupFromUrl =
    routerState.location.search.showNewsletterPopup;

  const { data: isUserAlreadySubscribedToNewsletters } =
    useIsUserSubscribedToNewsletter();

  const [hasSeenNewslettersPopup, setHasSeenNewslettersPopup] = useLocalStorage(
    'subscribe-newsletters-popup-seen',
    false,
  );

  const [hasWaitedInitialDelay, setHasWaitedInitialDelay] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasWaitedInitialDelay(true);
    }, INITIAL_DELAY);

    return () => clearTimeout(timer);
  }, []);

  const isSubscribeNewslettersPopupShown = useMemo(() => {
    if (isUserAlreadySubscribedToNewsletters) {
      return false;
    }

    // If showNewsletterPopupFromUrl is present, show popup immediately (bypass delay)
    if (showNewsletterPopupFromUrl) {
      return true;
    }

    // Otherwise, follow normal logic with delay and seen state
    if (!hasWaitedInitialDelay) {
      return false;
    }

    if (!hasSeenNewslettersPopup) {
      return true;
    }

    return false;
  }, [
    showNewsletterPopupFromUrl,
    hasSeenNewslettersPopup,
    isUserAlreadySubscribedToNewsletters,
    hasWaitedInitialDelay,
  ]);

  const closeNewslettersPopup = useCallback(() => {
    setHasSeenNewslettersPopup(true);
    navigate({
      replace: true,
      resetScroll: false,
    });
  }, [setHasSeenNewslettersPopup]);

  return {
    isSubscribeNewslettersPopupShown,
    closeNewslettersPopup,
  };
};
