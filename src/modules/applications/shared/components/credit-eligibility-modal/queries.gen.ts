/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../../../shared/api/models.gen';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type UserEligibilityQueryVariables = Types.Exact<{
  merchant_id: Types.Scalars['Int']['input'];
  pin: Types.Scalars['String']['input'];
  email: Types.Scalars['String']['input'];
  conditions_agreement: Types.Scalars['Boolean']['input'];
  newsletter_agreement: Types.Scalars['Boolean']['input'];
  is_user_identified_by_cashier: Types.Scalars['Boolean']['input'];
}>;


export type UserEligibilityQuery = { __typename?: 'Query', user_eligibility: string };


export const UserEligibilityDocument = gql`
    query UserEligibility($merchant_id: Int!, $pin: String!, $email: String!, $conditions_agreement: Boolean!, $newsletter_agreement: Boolean!, $is_user_identified_by_cashier: Boolean!) {
  user_eligibility(
    merchant_id: $merchant_id
    pin: $pin
    email: $email
    conditions_agreement: $conditions_agreement
    newsletter_agreement: $newsletter_agreement
    is_user_identified_by_cashier: $is_user_identified_by_cashier
  )
}
    `;

/**
 * __useUserEligibilityQuery__
 *
 * To run a query within a React component, call `useUserEligibilityQuery` and pass it any options that fit your needs.
 * When your component renders, `useUserEligibilityQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUserEligibilityQuery({
 *   variables: {
 *      merchant_id: // value for 'merchant_id'
 *      pin: // value for 'pin'
 *      email: // value for 'email'
 *      conditions_agreement: // value for 'conditions_agreement'
 *      newsletter_agreement: // value for 'newsletter_agreement'
 *      is_user_identified_by_cashier: // value for 'is_user_identified_by_cashier'
 *   },
 * });
 */
export function useUserEligibilityQuery(baseOptions: Apollo.QueryHookOptions<UserEligibilityQuery, UserEligibilityQueryVariables> & ({ variables: UserEligibilityQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<UserEligibilityQuery, UserEligibilityQueryVariables>(UserEligibilityDocument, options);
      }
export function useUserEligibilityLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<UserEligibilityQuery, UserEligibilityQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<UserEligibilityQuery, UserEligibilityQueryVariables>(UserEligibilityDocument, options);
        }
export function useUserEligibilitySuspenseQuery(baseOptions?: Apollo.SkipToken | Apollo.SuspenseQueryHookOptions<UserEligibilityQuery, UserEligibilityQueryVariables>) {
          const options = baseOptions === Apollo.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<UserEligibilityQuery, UserEligibilityQueryVariables>(UserEligibilityDocument, options);
        }
export type UserEligibilityQueryHookResult = ReturnType<typeof useUserEligibilityQuery>;
export type UserEligibilityLazyQueryHookResult = ReturnType<typeof useUserEligibilityLazyQuery>;
export type UserEligibilitySuspenseQueryHookResult = ReturnType<typeof useUserEligibilitySuspenseQuery>;
export type UserEligibilityQueryResult = Apollo.QueryResult<UserEligibilityQuery, UserEligibilityQueryVariables>;