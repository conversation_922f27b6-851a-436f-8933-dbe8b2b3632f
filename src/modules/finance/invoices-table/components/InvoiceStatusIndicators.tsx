import { <PERSON>ge, Button, HStack } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import {
  LocizeFinanceKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

type Props = {
  invoiceId: number;
  type: string;
  unsettledAmount: number;
};

export const InvoiceStatusIndicators = ({
  invoiceId,
  type,
  unsettledAmount,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.FINANCE);

  // For now, we'll consider all invoices as "new" since we don't have a specific field for this
  // This can be updated later when backend provides a proper "is_new" field
  const isNew = false; // TODO: Update when backend provides is_new field

  // An invoice is unsettled if it has an unpaid amount > 0
  const isUnsettled = unsettledAmount > 0;

  return (
    <HStack spacing={2}>
      {/* New invoice indicator - green or blue badge */}
      {isNew && (
        <Badge
          colorScheme={Math.random() > 0.5 ? 'green' : 'blue'}
          variant="solid"
          fontSize="xs"
          px={2}
          py={1}
          borderRadius="md"
        >
          {t(LocizeFinanceKeys.INVOICES_TABLE_STATUS_NEW)}
        </Badge>
      )}

      {/* Unsettled invoice indicator - red button */}
      {isUnsettled && (
        <Button
          size="xs"
          colorScheme="red"
          variant="solid"
          fontSize="xs"
          px={2}
          py={1}
          height="auto"
          minHeight="auto"
          borderRadius="md"
        >
          {t(LocizeFinanceKeys.INVOICES_TABLE_STATUS_UNSETTLED)}
        </Button>
      )}
    </HStack>
  );
};
