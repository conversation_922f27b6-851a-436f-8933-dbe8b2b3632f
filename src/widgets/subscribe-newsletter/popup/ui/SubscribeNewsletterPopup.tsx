import { Typography } from '@components/typography';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useUserNewsletterSubscriptionInfo } from '@entities/user';
import { useIsSubscribeNewslettersPopupShown } from '@pages/dashboard/hooks/useIsSubscribeNewslettersPopupShown';
import { Dialog } from '@radix-ui/react-dialog';
import { SmallModalLayout } from '@widgets/layouts/small-modal';
import { lazy, Suspense, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

const SubscribeNewsletterPopupForm = lazy(() =>
  import('./SubscribeNewsletterPopupForm').then((module) => ({
    default: module.SubscribeNewsletterPopupForm,
  })),
);

const SubscribeNewsletterPopupCtaButton = lazy(() =>
  import('./SubscribeNewsletterPopupCtaButton').then((module) => ({
    default: module.SubscribeNewsletterPopupCtaButton,
  })),
);

export const SubscribeNewsletterPopup = () => {
  const { isSubscribeNewslettersPopupShown, closeNewslettersPopup } =
    useIsSubscribeNewslettersPopupShown();

  const { data: userNewsletterSubscriptionInfo } =
    useUserNewsletterSubscriptionInfo();

  const { t } = useTranslation([LOCIZE_NAMESPACES.dashboard]);
  const [isNewsletterSubscribed, setIsNewsletterSubscribed] = useState(false);

  const handleNewsletterSubscribeComplete = useCallback(() => {
    setIsNewsletterSubscribed(true);

    setTimeout(() => {
      closeNewslettersPopup();
    }, 1500);
  }, [closeNewslettersPopup]);

  const handleDialogClose = useCallback(() => {
    if (isSubscribeNewslettersPopupShown) {
      closeNewslettersPopup();
    }
  }, [isSubscribeNewslettersPopupShown, closeNewslettersPopup]);

  const isAllRequiredDataExist = useMemo(
    () =>
      !!userNewsletterSubscriptionInfo?.email &&
      !!userNewsletterSubscriptionInfo?.phone,
    [
      userNewsletterSubscriptionInfo?.email,
      userNewsletterSubscriptionInfo?.phone,
    ],
  );

  if (!isSubscribeNewslettersPopupShown) {
    return null;
  }

  return (
    <Dialog
      open={isSubscribeNewslettersPopupShown}
      onOpenChange={handleDialogClose}
    >
      <Suspense>
        <SmallModalLayout
          backgroundImage="url('/images/dashboard/subscribe-newsletter.webp') lightgray 50% / cover no-repeat"
          backgroundImageClassName="h-64 md:h-32"
          icon={
            <div className="flex size-16 items-center justify-center rounded-full bg-primary-white">
              <span className="text-[34px]">🎉</span>
            </div>
          }
          closeIconColor="black"
        >
          {isNewsletterSubscribed ? (
            <div className="flex h-full items-center justify-center pb-6">
              <Typography variant="s">
                {t(LOCIZE_DASHBOARD_KEYS.newsletterPopupSubscribedTitle)}
              </Typography>
            </div>
          ) : (
            <div className="grid gap-6 text-center">
              <Typography variant="s">
                {t(LOCIZE_DASHBOARD_KEYS.newsletterPopupTitle)}
              </Typography>
              <Typography>
                {t(LOCIZE_DASHBOARD_KEYS.newsletterPopupDescription)}
              </Typography>
              {isAllRequiredDataExist ? (
                <SubscribeNewsletterPopupCtaButton
                  onCompleted={handleNewsletterSubscribeComplete}
                />
              ) : (
                <SubscribeNewsletterPopupForm
                  onCompleted={handleNewsletterSubscribeComplete}
                />
              )}
            </div>
          )}
        </SmallModalLayout>
      </Suspense>
    </Dialog>
  );
};
