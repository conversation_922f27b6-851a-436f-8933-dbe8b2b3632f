import { Typography } from '@components/typography';
import { Button, type ButtonProps } from '@components/ui/button';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import type { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useMount } from 'react-use';

import type {
  Application,
  ContractType,
  CreditAccount,
  User,
} from '@/shared/types';

import {
  type IdCardPrepareSignatureMutationVariables,
  type IdCardSignContractMutationVariables,
  useIdCardPrepareSignatureMutation,
  useIdCardSignContractMutation,
} from '../api/mutations.gen';
import { loadAndInitializeIdCardScripts } from '../utils';

type SigningAppletType = {
  isInitialized: boolean;
  init: (options: {
    certificatePurpose: 'sign' | string;
    codebase: string;
    language: string;
    supportedResidencies: string[];
  }) => void;
  start: () => void;
  setHashAlgorithm: (algo: string) => Promise<void>;
  sign: (
    dtbs: string,
    dtbsHash: string,
    callback: (signedHash: string) => void,
  ) => Promise<void> | void;
};

declare let iSignApplet: SigningAppletType;

type SigningByIdCardButtonProps = {
  userId?: User['id'];
  creditAccountId?: CreditAccount['id'];
  applicationId?: Application['id'];
  contractType: ContractType;
  buttonComponent?: FC<ButtonProps>;
  disabled?: boolean;
  onSuccess: () => void;
  onError: (error: unknown) => void;
  onReject: () => void;
  className?: string;
  label?: string;
};

const SigningByIdCardButton: FC<SigningByIdCardButtonProps> = ({
  userId,
  creditAccountId,
  applicationId,
  contractType,
  buttonComponent: ButtonComponent = Button,
  disabled,
  onSuccess,
  onError,
  onReject,
  className,
  label,
}) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);

  const { i18n } = useTranslation();

  useMount(() => {
    loadAndInitializeIdCardScripts();
  });

  const { mutateAsync: prepareForIdCardSigning } =
    useIdCardPrepareSignatureMutation();

  const prepareIdCardContractSignature = (
    variables: IdCardPrepareSignatureMutationVariables,
  ) => prepareForIdCardSigning(variables);

  const { mutateAsync: signIdCardContract, isPending } =
    useIdCardSignContractMutation();

  const signContractWithIdCard = (
    variables: IdCardSignContractMutationVariables,
  ) => signIdCardContract(variables);

  const signAppWithIdCard = () => {
    const lang = i18n.language;

    if (!iSignApplet.isInitialized) {
      iSignApplet.init({
        certificatePurpose: 'sign',
        codebase: `${window.location.protocol}//${window.location.host}/dokobit`,
        language: lang,
        supportedResidencies: ['ee'],
      });
    } else {
      iSignApplet.start();
    }

    window.certificateSelected = async (certificate: string) => {
      const cert = btoa(unescape(encodeURIComponent(certificate)));

      try {
        const res = await prepareIdCardContractSignature({
          creditAccountId,
          userId,
          certificate: cert,
          contractType,
          applicationId,
        });

        await iSignApplet.setHashAlgorithm(res.challenge?.algorithm ?? '');

        await iSignApplet.sign(
          res.challenge?.dtbs ?? '',
          res.challenge?.dtbs_hash ?? '',
          (signedHash: string) => {
            if (signedHash) {
              signContractWithIdCard({
                token: res.challenge?.token || '',
                signature: signedHash,
              })
                .then((data) => {
                  if (data?.success) {
                    onSuccess();
                  } else {
                    onReject();
                  }
                })
                .catch((err) => {
                  onError(err);
                });
            }
          },
        );
      } catch (err) {
        onError(err);
      }
    };
  };

  return (
    <div className="grid w-full max-w-80 gap-14">
      <Typography className="text-center">
        {t(LOCIZE_AUTH_KEYS.idCardInstruction)}
      </Typography>
      <ButtonComponent
        fullWidth
        loading={isPending}
        onClick={signAppWithIdCard}
        className={className}
        disabled={disabled}
      >
        {label || t(LOCIZE_AUTH_KEYS.continue)}
      </ButtonComponent>
    </div>
  );
};

export { SigningByIdCardButton };
