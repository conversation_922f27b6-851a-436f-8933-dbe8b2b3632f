import { Callout } from '@components/Callout';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { smartIdAuthApi } from '@features/auth/by-smartId/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { useIsCountry } from '@hooks/system';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { SupportedCountries } from '@/shared/types';
import { processGqlFormValidationErrors } from '@/shared/utils/parseGraphQLError';

import { SmartIdAuthFormSchema, type SmartIdAuthFormType } from '../schemes';

enum SmartIdAuthFormView {
  FORM = 0,
  POLLING = 1,
}

type SmartIdAuthFormProps = {
  onSuccess?: () => Promise<void>;
  onError?: () => void;
};

const SmartIdAuthForm = ({ onSuccess, onError }: SmartIdAuthFormProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);

  const [view, setView] = useState<SmartIdAuthFormView>(
    SmartIdAuthFormView.FORM,
  );

  const isLvOrLtCountry = useIsCountry([
    SupportedCountries.LV,
    SupportedCountries.LT,
  ]);

  const form = useForm<SmartIdAuthFormType>({
    resolver: zodResolver(SmartIdAuthFormSchema),
    defaultValues: {
      pin: '',
    },
  });

  const {
    data: smartIdLoginChallengeMutationData,
    mutateAsync: smartIdLoginChallenge,
  } = smartIdAuthApi.useSmartIdLoginChallengeMutation();
  const [isPolling, setIsPolling] = useState(false);
  const { data: smartIdLoginPollData, error: smartIdLoginPollError } =
    smartIdAuthApi.useSmartIdLoginQuery(
      {
        sessionId:
          smartIdLoginChallengeMutationData?.smart_id_login_challenge
            ?.session_id ?? '',
      },
      {
        enabled:
          isPolling &&
          !!smartIdLoginChallengeMutationData?.smart_id_login_challenge
            ?.session_id,
        refetchInterval: 5000,
      },
    );

  useEffect(() => {
    if (smartIdLoginPollData?.smart_id_login?.is_authenticated) {
      onSuccess?.();
      setView(SmartIdAuthFormView.FORM);
      setIsPolling(false);
    }
  }, [smartIdLoginPollData, onSuccess]);

  useEffect(() => {
    if (smartIdLoginPollError) {
      // @ts-expect-error smartIdLoginPollError is {}
      if (!smartIdLoginPollError?.graphQLErrors?.length) {
        return;
      }
      setView(SmartIdAuthFormView.FORM);
      setIsPolling(false);
      onError?.();
    }
  }, [smartIdLoginPollError, onError]);

  const handleFormSubmit = useCallback(
    async ({ pin }: SmartIdAuthFormType) => {
      try {
        const { smart_id_login_challenge } = await smartIdLoginChallenge({
          pin,
        });

        if (smart_id_login_challenge?.is_authenticated) {
          await onSuccess?.();
          return;
        }

        if (!smart_id_login_challenge?.session_id) {
          onError?.();
          return;
        }

        setView(SmartIdAuthFormView.POLLING);

        setIsPolling(true);
      } catch (error) {
        processGqlFormValidationErrors({
          error,
          setFormError: form.setError,
        });
      }
    },
    [smartIdLoginChallenge, onSuccess, onError, form.setError],
  );

  if (view === SmartIdAuthFormView.POLLING) {
    return (
      <div className="flex w-full max-w-80 flex-col items-center gap-4">
        <Typography className="text-center">
          {t(LOCIZE_AUTH_KEYS.verificationCodeMessage1)}
        </Typography>
        <Typography variant="s">
          {smartIdLoginChallengeMutationData?.smart_id_login_challenge
            ?.challenge_id ??
            smartIdLoginPollData?.smart_id_login?.challenge_id}
        </Typography>
        <Typography className="text-center">
          {t(LOCIZE_AUTH_KEYS.verificationCodeMessage2)}
        </Typography>
        <Button
          className="mt-10"
          fullWidth
          onClick={() => {
            setView(SmartIdAuthFormView.FORM);
            setIsPolling(false);
          }}
          variant="grey"
        >
          {t(LOCIZE_AUTH_KEYS.cancel)}
        </Button>
      </div>
    );
  }

  return (
    <div className="grid w-full max-w-80 gap-8">
      {isLvOrLtCountry && <Callout text={t(LOCIZE_AUTH_KEYS.smartIdCallout)} />}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(handleFormSubmit)}
          className="grid w-full max-w-80 gap-8"
        >
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem className="w-full md:max-w-80">
                <FormControl>
                  <Input
                    disabled={form.formState.isSubmitting}
                    placeholder={t(LOCIZE_AUTH_KEYS.idCode)}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button fullWidth loading={form.formState.isSubmitting} type="submit">
            {t(LOCIZE_AUTH_KEYS.continue)}
          </Button>
        </form>
      </Form>
    </div>
  );
};

export { SmartIdAuthForm };
