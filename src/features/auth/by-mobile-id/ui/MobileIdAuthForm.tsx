import { PhoneInput } from '@components/PhoneInput';
import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@components/ui/form';
import { Input } from '@components/ui/input';
import {
  LOCIZE_AUTH_KEYS,
  LOCIZE_ERROR_KEYS,
  LOCIZE_NAMESPACES,
} from '@config/locize';
import { mobileIdAuthApi } from '@features/auth/by-mobile-id/api';
import { zodResolver } from '@hookform/resolvers/zod';
import { processGqlFormValidationErrors } from '@utils/parseGraphQLError';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { MobileIdAuthFormSchema, type MobileIdAuthFormType } from '../schemes';

enum MobileIdAuthFormView {
  FORM = 0,
  POLLING = 1,
}

type MobileIdAuthFormProps = {
  onSuccess?: () => Promise<void>;
  onError?: () => void;
};

const MobileIdAuthForm = ({ onSuccess, onError }: MobileIdAuthFormProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);

  const [view, setView] = useState<MobileIdAuthFormView>(
    MobileIdAuthFormView.FORM,
  );

  const form = useForm<MobileIdAuthFormType>({
    resolver: zodResolver(MobileIdAuthFormSchema),
    defaultValues: {
      pin: '',
      phone: '',
    },
  });

  const {
    reset,
    mutateAsync: mobileIdLoginChallengeMutation,
    data: mobileIdLoginChallengeMutationData,
  } = mobileIdAuthApi.useMobileIdLoginChallengeMutation({
    onSuccess: () => {
      form.resetField('pin');
      form.resetField('phone');
    },
    onError: (errors: Array<{ message?: string }>) => {
      for (const error of errors) {
        if (error?.message === 'The entered phone number is invalid.') {
          form.setError('phone', {
            message: LOCIZE_ERROR_KEYS.isInvalidFormat,
          });
        }

        if (
          error?.message === 'The entered personal ID number is not correct.'
        ) {
          form.setError('pin', {
            message: LOCIZE_ERROR_KEYS.isInvalidFormat,
          });
        }
      }
    },
  });

  const [isPolling, setIsPolling] = useState(false);

  const { data: mobileIdLoginPollData, error: mobileIdLoginPollError } =
    mobileIdAuthApi.useMobileIdLoginQuery(
      {
        sessionId:
          mobileIdLoginChallengeMutationData?.mobile_login_challenge
            ?.session_id ?? '',
      },
      {
        enabled:
          isPolling &&
          !!mobileIdLoginChallengeMutationData?.mobile_login_challenge
            ?.session_id,
        refetchInterval: 5000,
      },
    );

  useEffect(() => {
    if (mobileIdLoginPollData?.mobile_login?.is_authenticated) {
      onSuccess?.();
      setIsPolling(false);
    }
  }, [mobileIdLoginPollData?.mobile_login?.is_authenticated, onSuccess]);

  useEffect(() => {
    if (mobileIdLoginPollError) {
      reset();
      setView(MobileIdAuthFormView.FORM);
      setIsPolling(false);
      onError?.();
    }
  }, [mobileIdLoginPollError, onError, reset]);

  const handleFormSubmit = useCallback(
    async ({ pin, phone }: MobileIdAuthFormType) => {
      try {
        const data = await mobileIdLoginChallengeMutation({
          pin,
          phone,
        });

        if (data?.mobile_login_challenge?.is_authenticated) {
          await onSuccess?.();
          return;
        }

        if (!data?.mobile_login_challenge?.session_id) {
          onError?.();
          return;
        }

        setView(MobileIdAuthFormView.POLLING);
        setIsPolling(true);
      } catch (error) {
        processGqlFormValidationErrors({
          error,
          setFormError: form.setError,
        });
      }
    },
    [mobileIdLoginChallengeMutation, onSuccess, onError, form.setError],
  );

  if (view === MobileIdAuthFormView.POLLING) {
    return (
      <div className="flex w-full flex-col items-center gap-4 md:max-w-80">
        <Typography className="text-center">
          {t(LOCIZE_AUTH_KEYS.verificationCodeMessage1)}
        </Typography>
        <Typography variant="s">
          {mobileIdLoginChallengeMutationData?.mobile_login_challenge
            ?.challenge_id ?? mobileIdLoginPollData?.mobile_login?.challenge_id}
        </Typography>
        <Typography className="text-center">
          {t(LOCIZE_AUTH_KEYS.verificationCodeMessage2)}
        </Typography>
        <Button
          className="mt-10"
          fullWidth
          onClick={() => {
            setView(MobileIdAuthFormView.FORM);
            setIsPolling(false);
          }}
          variant="grey"
        >
          {t(LOCIZE_AUTH_KEYS.cancel)}
        </Button>
      </div>
    );
  }

  return (
    <Form {...form}>
      <form
        className="grid w-full max-w-80 gap-8"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <div className="grid w-full gap-1">
          <FormField
            control={form.control}
            name="phone"
            render={({ field: { onChange, ...field } }) => (
              <FormItem className="w-full md:max-w-80">
                <FormControl>
                  <PhoneInput
                    disabled={form.formState.isSubmitting}
                    onValueChange={({ value }) => {
                      onChange(value);
                    }}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem className="w-full md:max-w-80">
                <FormControl>
                  <Input
                    disabled={form.formState.isSubmitting}
                    placeholder={t(LOCIZE_AUTH_KEYS.idCode)}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <Button fullWidth loading={form.formState.isSubmitting} type="submit">
          {t(LOCIZE_AUTH_KEYS.continue)}
        </Button>
      </form>
    </Form>
  );
};

export { MobileIdAuthForm };
