import {
  type EparakstsAuthorizationMethod,
  useEparakstsLoginChallengeMutation,
  useEparakstsLoginLazyQuery,
} from 'api/core/generated';
import { AppApiVersions, AppSearchParams } from 'app-constants';
import { webUrl } from 'environment';
import { useSearchParams } from 'react-router-dom';

export const useEParakstsLogin = () => {
  const [searchParams] = useSearchParams();

  const return_url = `${webUrl}/eparaksts-login`;

  const code = searchParams.get(AppSearchParams.eParakstsCode) ?? '';

  const [loginByEParaksts, { data, loading, error }] =
    useEparakstsLoginLazyQuery({
      context: { apiVersion: AppApiVersions.core },
      variables: {
        code,
        return_url,
      },
    });

  const [
    doEParakstsLoginChallenge,
    {
      data: challengeData,
      loading: challengeProcessing,
      error: challengeError,
    },
  ] = useEparakstsLoginChallengeMutation({
    context: { apiVersion: AppApiVersions.core },
  });

  const executeEParakstsLoginChallenge = (
    method: EparakstsAuthorizationMethod,
  ) =>
    doEParakstsLoginChallenge({
      variables: {
        method,
        return_url,
      },
    });

  return {
    loginByEParaksts,
    eParakstsLoginSuccess: Boolean(data?.success),
    eParakstsLoginProcessing: loading,
    eParakstsLoginError: error,
    executeEParakstsLoginChallenge,
    eParakstsLoginChallengeData: challengeData,
    eParakstsLoginChallengeProcessing: challengeProcessing,
    eParakstsLoginChallengeError: challengeError,
  };
};
