type Nullable<T> = T | null;
type Nullish<T> = T | null | undefined;
type ArrayElement<ArrayType extends readonly unknown[]> =
  ArrayType extends readonly (infer ElementType)[] ? ElementType : never;

type NonNullable<T> = T extends null | undefined ? never : T;

interface Window {
  _sift?: Array<string[] | { [key: string]: unknown }>;
  zE: (widget: string, method: string, ...args: unknown[]) => void;
  certificateSelected: (certificate: string) => void;
}
